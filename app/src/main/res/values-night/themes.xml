<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- iOS Style Dark Theme -->
    <style name="Theme.Alarm_clock_2" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color - iOS Blue -->
        <item name="colorPrimary">@color/ios_blue_light</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorPrimaryContainer">@color/ios_blue_dark</item>
        <item name="colorOnPrimaryContainer">@color/ios_blue_light</item>

        <!-- Secondary colors -->
        <item name="colorSecondary">@color/ios_mint</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorSecondaryContainer">@color/ios_teal</item>
        <item name="colorOnSecondaryContainer">@color/ios_mint</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/ios_background_primary_dark</item>
        <item name="colorSurface">@color/ios_surface_primary_dark</item>
        <item name="colorSurfaceVariant">@color/ios_surface_secondary_dark</item>
        <item name="colorOnBackground">@color/ios_text_primary_dark</item>
        <item name="colorOnSurface">@color/ios_text_primary_dark</item>
        <item name="colorOnSurfaceVariant">@color/ios_text_secondary_dark</item>

        <!-- Error colors -->
        <item name="colorError">@color/ios_red</item>
        <item name="colorOnError">@color/white</item>
        <item name="colorErrorContainer">@color/ios_red</item>
        <item name="colorOnErrorContainer">@color/white</item>

        <!-- Outline colors -->
        <item name="colorOutline">@color/ios_separator_opaque_dark</item>
        <item name="colorOutlineVariant">@color/ios_separator_non_opaque_dark</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/ios_background_primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/ios_background_primary_dark</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Window background -->
        <item name="android:windowBackground">@color/ios_background_primary_dark</item>
    </style>
</resources>